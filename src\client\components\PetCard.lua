-- 寵物卡片組件
-- MMORPG Pet Gacha Game - Pet Card Component

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local SimpleReact = require(ReplicatedStorage.Shared.modules.SimpleReact)
local Utilities = require(ReplicatedStorage.Shared.modules.Utilities)

-- 🎨 UI 顏色配置
local UI_COLORS = {
    CardBackground = Color3.fromRGB(50, 50, 60),
    TextPrimary = Color3.fromRGB(255, 255, 255),
    TextSecondary = Color3.fromRGB(200, 200, 200),
    ButtonHover = Color3.fromRGB(70, 70, 80),
    IconBackground = Color3.fromRGB(40, 40, 50)
}

-- 🐾 寵物元素表情符號
local function getPetEmoji(element)
    local emojis = {
        Fire = "🔥",
        Water = "💧",
        Earth = "🌱",
        Air = "💨",
        Light = "✨",
        Dark = "🌙"
    }
    return emojis[element] or "❓"
end

-- 🃏 寵物卡片組件
local PetCard = SimpleReact.createComponent(function(self)
    local petData = self.props.petData
    local onClick = self.props.onClick
    local index = self.props.index or 1
    
    -- 創建主卡片框架
    return SimpleReact.Frame({
        Name = "PetCard_" .. index,
        Size = UDim2.new(0, 140, 0, 170),
        BackgroundColor3 = UI_COLORS.CardBackground,
        BorderSizePixel = 0,
        children = {
            -- 圓角效果
            SimpleReact.createElement("UICorner", {
                CornerRadius = UDim.new(0, 8)
            }),
            
            -- 稀有度邊框
            SimpleReact.Frame({
                Name = "RarityBorder",
                Size = UDim2.new(1, 4, 1, 4),
                Position = UDim2.new(0, -2, 0, -2),
                BackgroundColor3 = Utilities.GetRarityColor(petData.Rarity),
                BorderSizePixel = 0,
                ZIndex = 0,
                children = {
                    SimpleReact.createElement("UICorner", {
                        CornerRadius = UDim.new(0, 10)
                    })
                }
            }),
            
            -- 寵物圖標
            SimpleReact.TextLabel({
                Name = "IconLabel",
                Size = UDim2.new(1, -10, 0, 70),
                Position = UDim2.new(0, 5, 0, 5),
                BackgroundColor3 = petData.IsDiscovered and UI_COLORS.IconBackground or Color3.fromRGB(60, 60, 60),
                Text = petData.IsDiscovered and getPetEmoji(petData.Element) or "❓",
                TextColor3 = UI_COLORS.TextPrimary,
                TextScaled = true,
                Font = Enum.Font.Gotham,
                children = {
                    SimpleReact.createElement("UICorner", {
                        CornerRadius = UDim.new(0, 6)
                    })
                }
            }),
            
            -- 寵物名稱
            SimpleReact.TextLabel({
                Name = "NameLabel",
                Size = UDim2.new(1, -10, 0, 25),
                Position = UDim2.new(0, 5, 0, 80),
                BackgroundTransparency = 1,
                Text = petData.IsDiscovered and petData.Name or "???",
                TextColor3 = UI_COLORS.TextPrimary,
                TextScaled = true,
                Font = Enum.Font.GothamBold
            }),
            
            -- 稀有度標籤
            SimpleReact.TextLabel({
                Name = "RarityLabel",
                Size = UDim2.new(1, -10, 0, 20),
                Position = UDim2.new(0, 5, 0, 105),
                BackgroundTransparency = 1,
                Text = petData.IsDiscovered and petData.Rarity or "???",
                TextColor3 = Utilities.GetRarityColor(petData.Rarity),
                TextScaled = true,
                Font = Enum.Font.Gotham
            }),
            
            -- 擁有狀態
            SimpleReact.TextLabel({
                Name = "OwnedLabel",
                Size = UDim2.new(1, -10, 0, 20),
                Position = UDim2.new(0, 5, 0, 130),
                BackgroundTransparency = 1,
                Text = petData.IsOwned and "✅ 已擁有" or (petData.IsDiscovered and "❌ 未擁有" or "❓ 未發現"),
                TextColor3 = petData.IsOwned and Color3.fromRGB(100, 255, 100) or UI_COLORS.TextSecondary,
                TextScaled = true,
                Font = Enum.Font.Gotham
            }),
            
            -- 點擊按鈕
            SimpleReact.TextButton({
                Name = "ClickButton",
                Size = UDim2.new(1, 0, 1, 0),
                Position = UDim2.new(0, 0, 0, 0),
                BackgroundTransparency = 1,
                Text = "",
                ZIndex = 10
            })
        }
    })
end)

-- 🎯 創建寵物卡片實例的便捷函數
local function createPetCard(petData, index, onClick)
    local card = PetCard.new({
        petData = petData,
        index = index,
        onClick = onClick
    })
    return card
end

-- 🎨 添加懸停效果的函數
local function addHoverEffects(cardInstance)
    local clickButton = cardInstance:FindFirstChild("ClickButton")
    if clickButton then
        clickButton.MouseEnter:Connect(function()
            TweenService:Create(
                cardInstance, 
                TweenInfo.new(0.2), 
                {BackgroundColor3 = UI_COLORS.ButtonHover}
            ):Play()
        end)
        
        clickButton.MouseLeave:Connect(function()
            TweenService:Create(
                cardInstance, 
                TweenInfo.new(0.2), 
                {BackgroundColor3 = UI_COLORS.CardBackground}
            ):Play()
        end)
    end
end

return {
    PetCard = PetCard,
    createPetCard = createPetCard,
    addHoverEffects = addHoverEffects
}
