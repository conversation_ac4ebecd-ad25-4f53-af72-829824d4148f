-- 服務端主啟動文件
-- MMORPG Pet Gacha Game Server

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 檢查 Knit 是否存在，如果不存在則使用備用方案
local Knit
local success, err = pcall(function()
    Knit = require(ReplicatedStorage.Packages.Knit)
end)

if not success then
    warn("⚠️ Knit 包未找到，使用簡化啟動模式")
    -- 創建簡化的服務管理器
    Knit = {
        Start = function()
            return {
                andThen = function(self, callback)
                    callback()
                    return self
                end,
                catch = function(self, callback)
                    return self
                end
            }
        end
    }
end

-- 載入所有服務
local services = script.services:GetChildren()
for _, serviceModule in pairs(services) do
    if serviceModule:IsA("ModuleScript") then
        require(serviceModule)
    end
end

-- 啟動 Knit 框架
Knit.Start():andThen(function()
    print("🚀 MMORPG Pet Gacha Server 已啟動!")
end):catch(function(err)
    warn("❌ 服務端啟動失敗:", err)
end)
