-- 服務端主啟動文件
-- MMORPG Pet Gacha Game Server

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Knit = require(ReplicatedStorage.Packages.Knit)

-- 載入所有服務
local services = script.services:GetChildren()
for _, serviceModule in pairs(services) do
    if serviceModule:IsA("ModuleScript") then
        require(serviceModule)
    end
end

-- 啟動 Knit 框架
Knit.Start():andThen(function()
    print("🚀 MMORPG Pet Gacha Server 已啟動!")
end):catch(function(err)
    warn("❌ 服務端啟動失敗:", err)
end)
