-- 遊戲數據類型定義
-- MMORPG Pet Gacha Game Types

local GameTypes = {}

-- 👤 角色數據類型
export type CharacterData = {
    UserId: number,
    Name: string,
    Level: number,
    Experience: number,
    Stats: {
        HP: number,
        MaxHP: number,
        MP: number,
        MaxMP: number,
        ATK: number,
        DEF: number,
        SPD: number
    },
    Equipment: {
        Weapon: string?,
        Armor: string?,
        Accessory: string?
    },
    Position: Vector3?,
    LastLogin: number
}

-- 🐾 寵物數據類型
export type PetData = {
    Id: string,
    SpeciesId: string,
    Name: string,
    Level: number,
    Experience: number,
    Rarity: "Common" | "Rare" | "Epic" | "Legendary",
    Element: "Fire" | "Water" | "Earth" | "Air" | "Light" | "Dark",
    Stats: {
        HP: number,
        MaxHP: number,
        ATK: number,
        DEF: number,
        SPD: number
    },
    Skills: {string},
    ObtainedAt: number,
    IsInTeam: boolean
}

-- 🎲 扭蛋數據類型
export type GachaData = {
    Type: "Normal" | "Premium",
    Cost: number,
    Results: {PetData},
    Timestamp: number,
    PityCount: number
}

-- 🎒 物品數據類型
export type ItemData = {
    Id: string,
    Name: string,
    Type: "Consumable" | "Equipment" | "Material",
    Rarity: "Common" | "Rare" | "Epic" | "Legendary",
    Description: string,
    Icon: string,
    Quantity: number,
    Properties: {[string]: any}
}

-- 💰 貨幣數據類型
export type CurrencyData = {
    Gold: number,
    Gems: number,
    LastUpdated: number
}

-- 📊 玩家完整數據類型
export type PlayerData = {
    Character: CharacterData,
    Pets: {[string]: PetData},
    PetTeam: {string},  -- 最多6隻寵物的ID陣列
    Inventory: {[string]: ItemData},
    Currency: CurrencyData,
    GachaHistory: {GachaData},
    PetDex: {
        Discovered: {[string]: boolean},
        FirstSeen: {[string]: number}
    },
    Achievements: {[string]: boolean},
    Settings: {
        MusicVolume: number,
        SFXVolume: number,
        AutoBattle: boolean
    },
    CreatedAt: number,
    LastSave: number
}

-- ⚔️ 戰鬥數據類型
export type BattleData = {
    Id: string,
    Type: "PvE" | "PvP",
    Players: {number},  -- UserId 陣列
    Teams: {
        [number]: {PetData}  -- UserId -> Pet陣列
    },
    CurrentTurn: number,
    TurnOrder: {number},  -- Pet索引順序
    Status: "Waiting" | "InProgress" | "Finished",
    Winner: number?,
    StartTime: number,
    EndTime: number?
}

-- 🏆 成就數據類型
export type AchievementData = {
    Id: string,
    Name: string,
    Description: string,
    Icon: string,
    Reward: {
        Gold: number?,
        Gems: number?,
        Items: {ItemData}?
    },
    Progress: number,
    MaxProgress: number,
    IsCompleted: boolean,
    CompletedAt: number?
}

return GameTypes
