-- 寵物圖鑑主組件
-- MMORPG Pet Gacha Game - Pet Dex Main Component

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local SimpleReact = require(ReplicatedStorage.Shared.modules.SimpleReact)
local PetCardModule = require(script.Parent.PetCard)

-- 🎨 UI 顏色配置
local UI_COLORS = {
    Background = Color3.fromRGB(30, 30, 40),
    HeaderBackground = Color3.fromRGB(40, 40, 50),
    TextPrimary = Color3.fromRGB(255, 255, 255),
    ProgressBar = Color3.fromRGB(100, 200, 100)
}

-- 🐾 測試寵物數據
local TEST_PETS = {
    {
        Id = "fire_dragon",
        Name = "火焰龍",
        Element = "Fire",
        Rarity = "Legendary",
        IsDiscovered = true,
        IsOwned = true,
        Description = "古老的火焰守護者，擁有毀滅性的火焰力量",
        Stats = { HP = 120, ATK = 25, DEF = 15, SPD = 18 },
        Skills = {"火球術", "龍息", "烈焰衝撞"}
    },
    {
        Id = "flame_fox",
        Name = "烈焰狐",
        Element = "Fire",
        Rarity = "Epic",
        IsDiscovered = true,
        IsOwned = false,
        Description = "敏捷的火系狐狸，以速度和靈活著稱",
        Stats = { HP = 80, ATK = 20, DEF = 10, SPD = 25 },
        Skills = {"火焰爪", "狐火", "疾風步"}
    },
    {
        Id = "water_turtle",
        Name = "水晶龜",
        Element = "Water",
        Rarity = "Rare",
        IsDiscovered = true,
        IsOwned = true,
        Description = "防禦力極強的水系寵物，擅長持久戰",
        Stats = { HP = 100, ATK = 15, DEF = 20, SPD = 8 },
        Skills = {"水炮", "防護殼", "治癒波"}
    },
    {
        Id = "forest_rabbit",
        Name = "森林兔",
        Element = "Earth",
        Rarity = "Common",
        IsDiscovered = true,
        IsOwned = true,
        Description = "森林中常見的可愛寵物，適合新手訓練師",
        Stats = { HP = 60, ATK = 12, DEF = 8, SPD = 20 },
        Skills = {"跳躍", "草葉飛刀", "逃脫"}
    },
    {
        Id = "light_angel",
        Name = "光明天使",
        Element = "Light",
        Rarity = "Legendary",
        IsDiscovered = false,
        IsOwned = false,
        Description = "來自天界的神聖生物，擁有強大的治癒和光明力量",
        Stats = { HP = 110, ATK = 22, DEF = 18, SPD = 20 },
        Skills = {"聖光術", "治癒之光", "天使之翼"}
    }
}

-- 📊 計算收集進度
local function calculateProgress()
    local owned = 0
    local discovered = 0
    local total = #TEST_PETS
    
    for _, petData in ipairs(TEST_PETS) do
        if petData.IsDiscovered then
            discovered = discovered + 1
        end
        if petData.IsOwned then
            owned = owned + 1
        end
    end
    
    return owned, discovered, total
end

-- 🏠 寵物圖鑑主組件
local PetDex = SimpleReact.createComponent(function(self)
    local owned, discovered, total = calculateProgress()
    
    return SimpleReact.ScreenGui({
        Name = "PetDexUI_Roact",
        ResetOnSpawn = false,
        children = {
            -- 主框架
            SimpleReact.Frame({
                Name = "MainFrame",
                Size = UDim2.new(0.8, 0, 0.8, 0),
                Position = UDim2.new(0.1, 0, 0.1, 0),
                BackgroundColor3 = UI_COLORS.Background,
                BorderSizePixel = 0,
                children = {
                    -- 圓角效果
                    SimpleReact.createElement("UICorner", {
                        CornerRadius = UDim.new(0, 12)
                    }),
                    
                    -- 標題欄
                    SimpleReact.Frame({
                        Name = "HeaderFrame",
                        Size = UDim2.new(1, 0, 0, 60),
                        Position = UDim2.new(0, 0, 0, 0),
                        BackgroundColor3 = UI_COLORS.HeaderBackground,
                        BorderSizePixel = 0,
                        children = {
                            -- 標題欄圓角
                            SimpleReact.createElement("UICorner", {
                                CornerRadius = UDim.new(0, 12)
                            }),
                            
                            -- 標題文字
                            SimpleReact.TextLabel({
                                Name = "TitleLabel",
                                Size = UDim2.new(0.6, 0, 1, 0),
                                Position = UDim2.new(0.05, 0, 0, 0),
                                BackgroundTransparency = 1,
                                Text = "🐾 寵物圖鑑 (Roact版)",
                                TextColor3 = UI_COLORS.TextPrimary,
                                TextScaled = true,
                                Font = Enum.Font.GothamBold
                            }),
                            
                            -- 進度顯示
                            SimpleReact.TextLabel({
                                Name = "ProgressLabel",
                                Size = UDim2.new(0.3, 0, 0.6, 0),
                                Position = UDim2.new(0.65, 0, 0.2, 0),
                                BackgroundColor3 = UI_COLORS.ProgressBar,
                                Text = string.format("收集: %d/%d", owned, total),
                                TextColor3 = UI_COLORS.TextPrimary,
                                TextScaled = true,
                                Font = Enum.Font.Gotham,
                                children = {
                                    SimpleReact.createElement("UICorner", {
                                        CornerRadius = UDim.new(0, 6)
                                    })
                                }
                            }),
                            
                            -- 關閉按鈕
                            SimpleReact.TextButton({
                                Name = "CloseButton",
                                Size = UDim2.new(0, 40, 0, 40),
                                Position = UDim2.new(1, -50, 0, 10),
                                BackgroundColor3 = Color3.fromRGB(200, 50, 50),
                                Text = "✕",
                                TextColor3 = UI_COLORS.TextPrimary,
                                TextScaled = true,
                                Font = Enum.Font.GothamBold,
                                children = {
                                    SimpleReact.createElement("UICorner", {
                                        CornerRadius = UDim.new(0, 8)
                                    })
                                }
                            })
                        }
                    }),
                    
                    -- 寵物網格容器
                    SimpleReact.ScrollingFrame({
                        Name = "PetGrid",
                        Size = UDim2.new(1, -20, 1, -80),
                        Position = UDim2.new(0, 10, 0, 70),
                        BackgroundTransparency = 1,
                        BorderSizePixel = 0,
                        ScrollBarThickness = 8,
                        children = {
                            -- 網格布局
                            SimpleReact.createElement("UIGridLayout", {
                                CellSize = UDim2.new(0, 140, 0, 170),
                                CellPadding = UDim2.new(0, 10, 0, 10),
                                SortOrder = Enum.SortOrder.Name
                            })
                        }
                    })
                }
            })
        }
    })
end)

return {
    PetDex = PetDex,
    TEST_PETS = TEST_PETS
}
