-- 客戶端主啟動文件
-- MMORPG Pet Gacha Game Client

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local Knit = require(ReplicatedStorage.Packages.Knit)

local Player = Players.LocalPlayer

-- 等待角色載入
if not Player.Character then
    Player.CharacterAdded:Wait()
end

-- 載入所有控制器
local controllers = script.controllers:GetChildren()
for _, controllerModule in pairs(controllers) do
    if controllerModule:IsA("ModuleScript") then
        require(controllerModule)
    end
end

-- 啟動 Knit 框架
Knit.Start():andThen(function()
    print("🎮 MMORPG Pet Gacha Client 已啟動!")
    print("👋 歡迎, " .. Player.Name .. "!")
end):catch(function(err)
    warn("❌ 客戶端啟動失敗:", err)
end)
