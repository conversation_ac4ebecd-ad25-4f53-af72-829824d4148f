-- 客戶端主啟動文件
-- MMORPG Pet Gacha Game Client

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- 檢查 Knit 是否存在，如果不存在則使用備用方案
local Knit
local success, err = pcall(function()
    Knit = require(ReplicatedStorage.Packages.Knit)
end)

if not success then
    warn("⚠️ Knit 包未找到，使用簡化啟動模式")
    -- 創建簡化的控制器管理器
    Knit = {
        Start = function()
            return {
                andThen = function(self, callback)
                    callback()
                    return self
                end,
                catch = function(self, callback)
                    return self
                end
            }
        end
    }
end

local Player = Players.LocalPlayer

-- 等待角色載入
if not Player.Character then
    Player.CharacterAdded:Wait()
end

-- 載入所有控制器
local controllers = script.controllers:GetChildren()
for _, controllerModule in pairs(controllers) do
    if controllerModule:IsA("ModuleScript") then
        require(controllerModule)
    end
end

-- 啟動 Knit 框架
Knit.Start():andThen(function()
    print("🎮 MMORPG Pet Gacha Client 已啟動!")
    print("👋 歡迎, " .. Player.Name .. "!")
end):catch(function(err)
    warn("❌ 客戶端啟動失敗:", err)
end)
