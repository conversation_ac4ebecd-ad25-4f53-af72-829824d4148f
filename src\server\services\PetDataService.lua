-- 寵物數據服務
-- MMORPG Pet Gacha Game - Pet Data Service

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- 導入共享模塊
local GameConstants = require(ReplicatedStorage.Shared.constants.GameConstants)
local GameTypes = require(ReplicatedStorage.Shared.types.GameTypes)
local Utilities = require(ReplicatedStorage.Shared.modules.Utilities)

local PetDataService = {}

-- 🐾 寵物種類數據庫
PetDataService.PetSpecies = {
    -- 火系寵物
    ["fire_dragon"] = {
        Id = "fire_dragon",
        Name = "火焰龍",
        Element = "Fire",
        BaseRarity = "Legendary",
        BaseStats = {
            HP = 120,
            ATK = 25,
            DEF = 15,
            SPD = 18
        },
        Skills = {"火球術", "龍息", "烈焰衝撞"},
        Description = "古老的火焰守護者，擁有毀滅性的火焰力量",
        ModelId = "fire_dragon_model",
        IconId = "fire_dragon_icon"
    },
    
    ["flame_fox"] = {
        Id = "flame_fox",
        Name = "烈焰狐",
        Element = "Fire",
        BaseRarity = "Epic",
        BaseStats = {
            HP = 80,
            ATK = 20,
            DEF = 10,
            SPD = 25
        },
        Skills = {"火焰爪", "狐火", "疾風步"},
        Description = "敏捷的火系狐狸，以速度和靈活著稱",
        ModelId = "flame_fox_model",
        IconId = "flame_fox_icon"
    },
    
    -- 水系寵物
    ["water_turtle"] = {
        Id = "water_turtle",
        Name = "水晶龜",
        Element = "Water",
        BaseRarity = "Rare",
        BaseStats = {
            HP = 100,
            ATK = 15,
            DEF = 20,
            SPD = 8
        },
        Skills = {"水炮", "防護殼", "治癒波"},
        Description = "防禦力極強的水系寵物，擅長持久戰",
        ModelId = "water_turtle_model",
        IconId = "water_turtle_icon"
    },
    
    -- 普通寵物
    ["forest_rabbit"] = {
        Id = "forest_rabbit",
        Name = "森林兔",
        Element = "Earth",
        BaseRarity = "Common",
        BaseStats = {
            HP = 60,
            ATK = 12,
            DEF = 8,
            SPD = 20
        },
        Skills = {"跳躍", "草葉飛刀", "逃脫"},
        Description = "森林中常見的可愛寵物，適合新手訓練師",
        ModelId = "forest_rabbit_model",
        IconId = "forest_rabbit_icon"
    },

    -- 光系寵物
    ["light_angel"] = {
        Id = "light_angel",
        Name = "光明天使",
        Element = "Light",
        BaseRarity = "Legendary",
        BaseStats = {
            HP = 110,
            ATK = 22,
            DEF = 18,
            SPD = 20
        },
        Skills = {"聖光術", "治癒之光", "天使之翼"},
        Description = "來自天界的神聖生物，擁有強大的治癒和光明力量",
        ModelId = "light_angel_model",
        IconId = "light_angel_icon"
    }
}

-- 🎲 根據稀有度獲取寵物列表
function PetDataService:GetPetsByRarity(rarity)
    local pets = {}
    for id, species in pairs(self.PetSpecies) do
        if species.BaseRarity == rarity then
            table.insert(pets, species)
        end
    end
    return pets
end

-- 🆔 生成新寵物實例
function PetDataService:CreatePetInstance(speciesId, level)
    local species = self.PetSpecies[speciesId]
    if not species then
        warn("❌ 未知的寵物種類:", speciesId)
        return nil
    end
    
    level = level or 1
    
    -- 計算等級加成後的屬性
    local levelMultiplier = 1 + (level - 1) * 0.1
    
    local petInstance = {
        Id = Utilities.GenerateId(12),
        SpeciesId = speciesId,
        Name = species.Name,
        Level = level,
        Experience = 0,
        Rarity = species.BaseRarity,
        Element = species.Element,
        Stats = {
            HP = math.floor(species.BaseStats.HP * levelMultiplier),
            MaxHP = math.floor(species.BaseStats.HP * levelMultiplier),
            ATK = math.floor(species.BaseStats.ATK * levelMultiplier),
            DEF = math.floor(species.BaseStats.DEF * levelMultiplier),
            SPD = math.floor(species.BaseStats.SPD * levelMultiplier)
        },
        Skills = Utilities.DeepCopy(species.Skills),
        ObtainedAt = Utilities.GetTimestamp(),
        IsInTeam = false
    }
    
    return petInstance
end

-- 📊 計算寵物戰鬥力
function PetDataService:CalculatePetPower(petData)
    local stats = petData.Stats
    return stats.HP + stats.ATK * 2 + stats.DEF + stats.SPD
end

-- 🔄 寵物升級
function PetDataService:LevelUpPet(petData)
    if petData.Level >= GameConstants.PET.MAX_LEVEL then
        return false, "寵物已達到最高等級"
    end
    
    local expRequired = Utilities.CalculateExpRequired(petData.Level + 1)
    if petData.Experience < expRequired then
        return false, "經驗值不足"
    end
    
    -- 升級
    petData.Level = petData.Level + 1
    petData.Experience = petData.Experience - expRequired
    
    -- 更新屬性
    local species = self.PetSpecies[petData.SpeciesId]
    local levelMultiplier = 1 + (petData.Level - 1) * 0.1
    
    petData.Stats.MaxHP = math.floor(species.BaseStats.HP * levelMultiplier)
    petData.Stats.HP = petData.Stats.MaxHP  -- 升級時恢復滿血
    petData.Stats.ATK = math.floor(species.BaseStats.ATK * levelMultiplier)
    petData.Stats.DEF = math.floor(species.BaseStats.DEF * levelMultiplier)
    petData.Stats.SPD = math.floor(species.BaseStats.SPD * levelMultiplier)
    
    return true, "升級成功"
end

-- 🔍 獲取寵物詳細信息
function PetDataService:GetPetInfo(speciesId)
    return self.PetSpecies[speciesId]
end

-- 📋 獲取所有寵物種類
function PetDataService:GetAllSpecies()
    return self.PetSpecies
end

-- 🎯 初始化服務
function PetDataService:Init()
    print("🐾 寵物數據服務已初始化")
    print("📊 載入了", #self:GetAllSpecies(), "種寵物")
end

return PetDataService
