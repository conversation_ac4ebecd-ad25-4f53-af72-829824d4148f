-- 寵物圖鑑UI控制器 (Roact版本)
-- MMORPG Pet Gacha Game - Pet Dex Controller (Roact Version)

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

-- 導入模塊
local SimpleReact = require(ReplicatedStorage.Shared.modules.SimpleReact)

local Player = Players.LocalPlayer
local PlayerGui = Player and Player:WaitForChild("PlayerGui", 5)

local PetDexController = {}

-- 🎯 組件實例
PetDexController.currentUI = nil
PetDexController.petCards = {}

-- 🐾 測試寵物數據
local TEST_PETS = {
    {Name = "火焰龍", Element = "Fire", Rarity = "Legendary", IsDiscovered = true, IsOwned = true},
    {Name = "烈焰狐", Element = "Fire", Rarity = "Epic", IsDiscovered = true, IsOwned = false},
    {Name = "水晶龜", Element = "Water", Rarity = "Rare", IsDiscovered = true, IsOwned = true},
    {Name = "森林兔", Element = "Earth", Rarity = "Common", IsDiscovered = true, IsOwned = true},
    {Name = "光明天使", Element = "Light", Rarity = "Legendary", IsDiscovered = false, IsOwned = false}
}

-- 🚀 打開寵物圖鑑UI (Roact版本)
function PetDexController:OpenUI()
    if self.currentUI then
        self:CloseUI()
    end
    
    print("🐾 開始創建Roact版寵物圖鑑UI...")
    
    -- 掛載到 PlayerGui 或 StarterGui
    local parent = PlayerGui or game:GetService("StarterGui")
    
    -- 使用 SimpleReact 創建UI
    self.currentUI = self:createMainUI(parent)
    
    print("🐾 Roact版寵物圖鑑UI已打開")
end

-- 🏠 創建主UI
function PetDexController:createMainUI(parent)
    -- 使用 SimpleReact 創建組件
    local mainComponent = SimpleReact.Component.new({})
    
    function mainComponent:render()
        return SimpleReact.ScreenGui({
            Name = "PetDexUI_Roact",
            ResetOnSpawn = false,
            children = {
                SimpleReact.Frame({
                    Name = "MainFrame",
                    Size = UDim2.new(0.8, 0, 0.8, 0),
                    Position = UDim2.new(0.1, 0, 0.1, 0),
                    BackgroundColor3 = Color3.fromRGB(30, 30, 40),
                    BorderSizePixel = 0,
                    children = {
                        SimpleReact.createElement("UICorner", {
                            CornerRadius = UDim.new(0, 12)
                        }),
                        
                        -- 標題欄
                        SimpleReact.Frame({
                            Name = "HeaderFrame",
                            Size = UDim2.new(1, 0, 0, 60),
                            BackgroundColor3 = Color3.fromRGB(40, 40, 50),
                            BorderSizePixel = 0,
                            children = {
                                SimpleReact.createElement("UICorner", {
                                    CornerRadius = UDim.new(0, 12)
                                }),
                                
                                SimpleReact.TextLabel({
                                    Name = "TitleLabel",
                                    Size = UDim2.new(0.6, 0, 1, 0),
                                    Position = UDim2.new(0.05, 0, 0, 0),
                                    BackgroundTransparency = 1,
                                    Text = "🐾 寵物圖鑑 (Roact版)",
                                    TextColor3 = Color3.fromRGB(255, 255, 255),
                                    TextScaled = true,
                                    Font = Enum.Font.GothamBold
                                }),
                                
                                SimpleReact.TextLabel({
                                    Name = "ProgressLabel",
                                    Size = UDim2.new(0.3, 0, 0.6, 0),
                                    Position = UDim2.new(0.65, 0, 0.2, 0),
                                    BackgroundColor3 = Color3.fromRGB(100, 200, 100),
                                    Text = "收集: 3/5",
                                    TextColor3 = Color3.fromRGB(255, 255, 255),
                                    TextScaled = true,
                                    Font = Enum.Font.Gotham,
                                    children = {
                                        SimpleReact.createElement("UICorner", {
                                            CornerRadius = UDim.new(0, 6)
                                        })
                                    }
                                }),
                                
                                SimpleReact.TextButton({
                                    Name = "CloseButton",
                                    Size = UDim2.new(0, 40, 0, 40),
                                    Position = UDim2.new(1, -50, 0, 10),
                                    BackgroundColor3 = Color3.fromRGB(200, 50, 50),
                                    Text = "✕",
                                    TextColor3 = Color3.fromRGB(255, 255, 255),
                                    TextScaled = true,
                                    Font = Enum.Font.GothamBold,
                                    children = {
                                        SimpleReact.createElement("UICorner", {
                                            CornerRadius = UDim.new(0, 8)
                                        })
                                    }
                                })
                            }
                        }),
                        
                        -- 寵物網格
                        SimpleReact.ScrollingFrame({
                            Name = "PetGrid",
                            Size = UDim2.new(1, -20, 1, -80),
                            Position = UDim2.new(0, 10, 0, 70),
                            BackgroundTransparency = 1,
                            BorderSizePixel = 0,
                            ScrollBarThickness = 8,
                            children = {
                                SimpleReact.createElement("UIGridLayout", {
                                    CellSize = UDim2.new(0, 140, 0, 170),
                                    CellPadding = UDim2.new(0, 10, 0, 10),
                                    SortOrder = Enum.SortOrder.Name
                                })
                            }
                        })
                    }
                })
            }
        })
    end
    
    local uiInstance = mainComponent:mount(parent)
    
    -- 創建寵物卡片
    self:createPetCards(uiInstance)
    
    -- 設置關閉按鈕
    self:setupCloseButton(uiInstance)
    
    return uiInstance
end

-- 🃏 創建寵物卡片
function PetDexController:createPetCards(uiInstance)
    local gridFrame = uiInstance:FindFirstChild("MainFrame")
    if gridFrame then
        gridFrame = gridFrame:FindFirstChild("PetGrid")
    end
    
    if not gridFrame then
        warn("❌ 未找到寵物網格容器")
        return
    end
    
    -- 創建寵物卡片
    for i, petData in ipairs(TEST_PETS) do
        self:createSinglePetCard(gridFrame, petData, i)
    end
    
    -- 更新網格大小
    local gridLayout = gridFrame:FindFirstChild("UIGridLayout")
    if gridLayout then
        gridLayout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
            gridFrame.CanvasSize = UDim2.new(0, 0, 0, gridLayout.AbsoluteContentSize.Y + 20)
        end)
    end
end

-- 🎴 創建單個寵物卡片
function PetDexController:createSinglePetCard(gridFrame, petData, index)
    local Utilities = require(ReplicatedStorage.Shared.modules.Utilities)

    -- 元素表情符號
    local function getPetEmoji(element)
        local emojis = {Fire = "🔥", Water = "💧", Earth = "🌱", Light = "✨"}
        return emojis[element] or "❓"
    end

    -- 創建卡片組件
    local cardComponent = SimpleReact.Component.new({})

    function cardComponent:render()
        return SimpleReact.Frame({
            Name = "PetCard_" .. index,
            Size = UDim2.new(0, 140, 0, 170),
            BackgroundColor3 = Color3.fromRGB(50, 50, 60),
            BorderSizePixel = 0,
            children = {
                SimpleReact.createElement("UICorner", {
                    CornerRadius = UDim.new(0, 8)
                }),

                -- 稀有度邊框
                SimpleReact.Frame({
                    Name = "RarityBorder",
                    Size = UDim2.new(1, 4, 1, 4),
                    Position = UDim2.new(0, -2, 0, -2),
                    BackgroundColor3 = Utilities.GetRarityColor(petData.Rarity),
                    BorderSizePixel = 0,
                    ZIndex = 0,
                    children = {
                        SimpleReact.createElement("UICorner", {
                            CornerRadius = UDim.new(0, 10)
                        })
                    }
                }),

                -- 寵物圖標
                SimpleReact.TextLabel({
                    Name = "IconLabel",
                    Size = UDim2.new(1, -10, 0, 70),
                    Position = UDim2.new(0, 5, 0, 5),
                    BackgroundColor3 = petData.IsDiscovered and Color3.fromRGB(40, 40, 50) or Color3.fromRGB(60, 60, 60),
                    Text = petData.IsDiscovered and getPetEmoji(petData.Element) or "❓",
                    TextColor3 = Color3.fromRGB(255, 255, 255),
                    TextScaled = true,
                    Font = Enum.Font.Gotham,
                    children = {
                        SimpleReact.createElement("UICorner", {
                            CornerRadius = UDim.new(0, 6)
                        })
                    }
                }),

                -- 寵物名稱
                SimpleReact.TextLabel({
                    Name = "NameLabel",
                    Size = UDim2.new(1, -10, 0, 25),
                    Position = UDim2.new(0, 5, 0, 80),
                    BackgroundTransparency = 1,
                    Text = petData.IsDiscovered and petData.Name or "???",
                    TextColor3 = Color3.fromRGB(255, 255, 255),
                    TextScaled = true,
                    Font = Enum.Font.GothamBold
                }),

                -- 稀有度標籤
                SimpleReact.TextLabel({
                    Name = "RarityLabel",
                    Size = UDim2.new(1, -10, 0, 20),
                    Position = UDim2.new(0, 5, 0, 105),
                    BackgroundTransparency = 1,
                    Text = petData.IsDiscovered and petData.Rarity or "???",
                    TextColor3 = Utilities.GetRarityColor(petData.Rarity),
                    TextScaled = true,
                    Font = Enum.Font.Gotham
                }),

                -- 擁有狀態
                SimpleReact.TextLabel({
                    Name = "OwnedLabel",
                    Size = UDim2.new(1, -10, 0, 20),
                    Position = UDim2.new(0, 5, 0, 130),
                    BackgroundTransparency = 1,
                    Text = petData.IsOwned and "✅ 已擁有" or (petData.IsDiscovered and "❌ 未擁有" or "❓ 未發現"),
                    TextColor3 = petData.IsOwned and Color3.fromRGB(100, 255, 100) or Color3.fromRGB(200, 200, 200),
                    TextScaled = true,
                    Font = Enum.Font.Gotham
                }),

                -- 點擊按鈕
                SimpleReact.TextButton({
                    Name = "ClickButton",
                    Size = UDim2.new(1, 0, 1, 0),
                    BackgroundTransparency = 1,
                    Text = "",
                    ZIndex = 10
                })
            }
        })
    end

    local cardInstance = cardComponent:mount(gridFrame)

    -- 添加點擊事件
    local clickButton = cardInstance:FindFirstChild("ClickButton")
    if clickButton then
        clickButton.MouseButton1Click:Connect(function()
            self:onPetCardClick(petData)
        end)
    end

    print("✅ 創建Roact寵物卡片: " .. petData.Name)
    return cardInstance
end

-- 🖱️ 寵物卡片點擊事件
function PetDexController:onPetCardClick(petData)
    if petData.IsDiscovered then
        print("🔍 點擊了寵物: " .. petData.Name)
    else
        print("❓ 這隻寵物尚未發現")
    end
end

-- 🔘 設置關閉按鈕
function PetDexController:setupCloseButton(uiInstance)
    local closeButton = uiInstance:FindFirstChild("MainFrame")
    if closeButton then
        closeButton = closeButton:FindFirstChild("HeaderFrame")
        if closeButton then
            closeButton = closeButton:FindFirstChild("CloseButton")
        end
    end

    if closeButton then
        closeButton.MouseButton1Click:Connect(function()
            self:CloseUI()
        end)
    end
end

-- ❌ 關閉UI
function PetDexController:CloseUI()
    if self.currentUI then
        self.currentUI:Destroy()
        self.currentUI = nil
        self.petCards = {}
        print("🐾 Roact版寵物圖鑑UI已關閉")
    end
end

-- 🎯 初始化控制器
function PetDexController:Init()
    print("🐾 Roact版寵物圖鑑控制器已初始化")

    -- 綁定快捷鍵 (P鍵打開圖鑑)
    if UserInputService then
        UserInputService.InputBegan:Connect(function(input, gameProcessed)
            if gameProcessed then return end

            if input.KeyCode == Enum.KeyCode.P then
                if self.currentUI then
                    self:CloseUI()
                else
                    self:OpenUI()
                end
            end
        end)
    end

    -- 自動打開UI進行測試
    spawn(function()
        wait(2)
        self:OpenUI()
    end)
end

-- 🎮 啟動控制器
PetDexController:Init()

return PetDexController
