-- 寵物圖鑑UI控制器
-- MMORPG Pet Gacha Game - Pet Dex Controller

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

-- 導入共享模塊
local GameConstants = require(ReplicatedStorage.Shared.constants.GameConstants)
local Utilities = require(ReplicatedStorage.Shared.modules.Utilities)

local Player = Players.LocalPlayer
local PlayerGui = Player:WaitForChild("PlayerGui")

local PetDexController = {}

-- 🎨 UI 顏色配置
local UI_COLORS = {
    Background = Color3.fromRGB(30, 30, 40),
    CardBackground = Color3.fromRGB(50, 50, 60),
    HeaderBackground = Color3.fromRGB(40, 40, 50),
    TextPrimary = Color3.fromRGB(255, 255, 255),
    TextSecondary = Color3.fromRGB(200, 200, 200),
    ButtonHover = Color3.fromRGB(70, 70, 80),
    ProgressBar = Color3.fromRGB(100, 200, 100)
}

-- 🐾 測試寵物數據 (模擬從服務端獲取)
local TEST_PETS = {
    {
        Id = "fire_dragon",
        Name = "火焰龍",
        Element = "Fire",
        Rarity = "Legendary",
        IsDiscovered = true,
        IsOwned = true,
        Description = "古老的火焰守護者，擁有毀滅性的火焰力量",
        Stats = { HP = 120, ATK = 25, DEF = 15, SPD = 18 },
        Skills = {"火球術", "龍息", "烈焰衝撞"}
    },
    {
        Id = "flame_fox",
        Name = "烈焰狐",
        Element = "Fire",
        Rarity = "Epic",
        IsDiscovered = true,
        IsOwned = false,
        Description = "敏捷的火系狐狸，以速度和靈活著稱",
        Stats = { HP = 80, ATK = 20, DEF = 10, SPD = 25 },
        Skills = {"火焰爪", "狐火", "疾風步"}
    },
    {
        Id = "water_turtle",
        Name = "水晶龜",
        Element = "Water",
        Rarity = "Rare",
        IsDiscovered = true,
        IsOwned = true,
        Description = "防禦力極強的水系寵物，擅長持久戰",
        Stats = { HP = 100, ATK = 15, DEF = 20, SPD = 8 },
        Skills = {"水炮", "防護殼", "治癒波"}
    },
    {
        Id = "forest_rabbit",
        Name = "森林兔",
        Element = "Earth",
        Rarity = "Common",
        IsDiscovered = true,
        IsOwned = true,
        Description = "森林中常見的可愛寵物，適合新手訓練師",
        Stats = { HP = 60, ATK = 12, DEF = 8, SPD = 20 },
        Skills = {"跳躍", "草葉飛刀", "逃脫"}
    },
    {
        Id = "light_angel",
        Name = "光明天使",
        Element = "Light",
        Rarity = "Legendary",
        IsDiscovered = false,
        IsOwned = false,
        Description = "來自天界的神聖生物，擁有強大的治癒和光明力量",
        Stats = { HP = 110, ATK = 22, DEF = 18, SPD = 20 },
        Skills = {"聖光術", "治癒之光", "天使之翼"}
    }
}

-- 🎯 創建主UI框架
function PetDexController:CreateMainUI()
    -- 創建主ScreenGui
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "PetDexUI"
    screenGui.ResetOnSpawn = false
    screenGui.Parent = PlayerGui
    
    -- 主框架
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0.8, 0, 0.8, 0)
    mainFrame.Position = UDim2.new(0.1, 0, 0.1, 0)
    mainFrame.BackgroundColor3 = UI_COLORS.Background
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = screenGui
    
    -- 圓角
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = mainFrame
    
    -- 標題欄
    local headerFrame = Instance.new("Frame")
    headerFrame.Name = "HeaderFrame"
    headerFrame.Size = UDim2.new(1, 0, 0, 60)
    headerFrame.Position = UDim2.new(0, 0, 0, 0)
    headerFrame.BackgroundColor3 = UI_COLORS.HeaderBackground
    headerFrame.BorderSizePixel = 0
    headerFrame.Parent = mainFrame
    
    local headerCorner = Instance.new("UICorner")
    headerCorner.CornerRadius = UDim.new(0, 12)
    headerCorner.Parent = headerFrame
    
    -- 標題文字
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "TitleLabel"
    titleLabel.Size = UDim2.new(0.7, 0, 1, 0)
    titleLabel.Position = UDim2.new(0.05, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "🐾 寵物圖鑑"
    titleLabel.TextColor3 = UI_COLORS.TextPrimary
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.GothamBold
    titleLabel.Parent = headerFrame
    
    -- 進度顯示
    local progressLabel = Instance.new("TextLabel")
    progressLabel.Name = "ProgressLabel"
    progressLabel.Size = UDim2.new(0.25, 0, 0.6, 0)
    progressLabel.Position = UDim2.new(0.7, 0, 0.2, 0)
    progressLabel.BackgroundColor3 = UI_COLORS.ProgressBar
    progressLabel.Text = "收集進度: 3/5"
    progressLabel.TextColor3 = UI_COLORS.TextPrimary
    progressLabel.TextScaled = true
    progressLabel.Font = Enum.Font.Gotham
    progressLabel.Parent = headerFrame
    
    local progressCorner = Instance.new("UICorner")
    progressCorner.CornerRadius = UDim.new(0, 6)
    progressCorner.Parent = progressLabel
    
    -- 關閉按鈕
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 40, 0, 40)
    closeButton.Position = UDim2.new(1, -50, 0, 10)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "✕"
    closeButton.TextColor3 = UI_COLORS.TextPrimary
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.GothamBold
    closeButton.Parent = headerFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 8)
    closeCorner.Parent = closeButton
    
    -- 關閉按鈕事件
    closeButton.MouseButton1Click:Connect(function()
        self:CloseUI()
    end)
    
    return screenGui, mainFrame
end

-- 📋 創建寵物網格
function PetDexController:CreatePetGrid(parentFrame)
    local gridFrame = Instance.new("ScrollingFrame")
    gridFrame.Name = "PetGrid"
    gridFrame.Size = UDim2.new(0.65, 0, 1, -70)
    gridFrame.Position = UDim2.new(0.02, 0, 0, 70)
    gridFrame.BackgroundTransparency = 1
    gridFrame.BorderSizePixel = 0
    gridFrame.ScrollBarThickness = 8
    gridFrame.Parent = parentFrame
    
    local gridLayout = Instance.new("UIGridLayout")
    gridLayout.CellSize = UDim2.new(0, 150, 0, 180)
    gridLayout.CellPadding = UDim2.new(0, 10, 0, 10)
    gridLayout.SortOrder = Enum.SortOrder.Name
    gridLayout.Parent = gridFrame
    
    -- 創建寵物卡片
    for i, petData in ipairs(TEST_PETS) do
        self:CreatePetCard(gridFrame, petData, i)
    end
    
    -- 更新滾動框大小
    gridLayout:GetPropertyChangedSignal("AbsoluteContentSize"):Connect(function()
        gridFrame.CanvasSize = UDim2.new(0, 0, 0, gridLayout.AbsoluteContentSize.Y + 20)
    end)
    
    return gridFrame
end

-- 🃏 創建寵物卡片
function PetDexController:CreatePetCard(parentFrame, petData, index)
    local cardFrame = Instance.new("Frame")
    cardFrame.Name = "PetCard_" .. index
    cardFrame.Size = UDim2.new(0, 150, 0, 180)
    cardFrame.BackgroundColor3 = UI_COLORS.CardBackground
    cardFrame.BorderSizePixel = 0
    cardFrame.Parent = parentFrame

    local cardCorner = Instance.new("UICorner")
    cardCorner.CornerRadius = UDim.new(0, 8)
    cardCorner.Parent = cardFrame

    -- 稀有度邊框
    local rarityBorder = Instance.new("Frame")
    rarityBorder.Name = "RarityBorder"
    rarityBorder.Size = UDim2.new(1, 4, 1, 4)
    rarityBorder.Position = UDim2.new(0, -2, 0, -2)
    rarityBorder.BackgroundColor3 = Utilities.GetRarityColor(petData.Rarity)
    rarityBorder.BorderSizePixel = 0
    rarityBorder.Parent = cardFrame

    local borderCorner = Instance.new("UICorner")
    borderCorner.CornerRadius = UDim.new(0, 10)
    borderCorner.Parent = rarityBorder

    -- 寵物圖標 (暫時用文字代替)
    local iconLabel = Instance.new("TextLabel")
    iconLabel.Name = "IconLabel"
    iconLabel.Size = UDim2.new(1, -10, 0, 80)
    iconLabel.Position = UDim2.new(0, 5, 0, 5)
    iconLabel.BackgroundColor3 = Color3.fromRGB(40, 40, 50)
    iconLabel.Text = self:GetPetEmoji(petData.Element)
    iconLabel.TextColor3 = UI_COLORS.TextPrimary
    iconLabel.TextScaled = true
    iconLabel.Font = Enum.Font.Gotham
    iconLabel.Parent = cardFrame

    local iconCorner = Instance.new("UICorner")
    iconCorner.CornerRadius = UDim.new(0, 6)
    iconCorner.Parent = iconLabel

    -- 如果未發現，顯示問號
    if not petData.IsDiscovered then
        iconLabel.Text = "❓"
        iconLabel.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    end

    -- 寵物名稱
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Name = "NameLabel"
    nameLabel.Size = UDim2.new(1, -10, 0, 25)
    nameLabel.Position = UDim2.new(0, 5, 0, 90)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = petData.IsDiscovered and petData.Name or "???"
    nameLabel.TextColor3 = UI_COLORS.TextPrimary
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.GothamBold
    nameLabel.Parent = cardFrame

    -- 稀有度標籤
    local rarityLabel = Instance.new("TextLabel")
    rarityLabel.Name = "RarityLabel"
    rarityLabel.Size = UDim2.new(1, -10, 0, 20)
    rarityLabel.Position = UDim2.new(0, 5, 0, 115)
    rarityLabel.BackgroundTransparency = 1
    rarityLabel.Text = petData.IsDiscovered and petData.Rarity or "???"
    rarityLabel.TextColor3 = Utilities.GetRarityColor(petData.Rarity)
    rarityLabel.TextScaled = true
    rarityLabel.Font = Enum.Font.Gotham
    rarityLabel.Parent = cardFrame

    -- 擁有狀態
    local ownedLabel = Instance.new("TextLabel")
    ownedLabel.Name = "OwnedLabel"
    ownedLabel.Size = UDim2.new(1, -10, 0, 20)
    ownedLabel.Position = UDim2.new(0, 5, 0, 140)
    ownedLabel.BackgroundTransparency = 1
    ownedLabel.Text = petData.IsOwned and "✅ 已擁有" or (petData.IsDiscovered and "❌ 未擁有" or "❓ 未發現")
    ownedLabel.TextColor3 = petData.IsOwned and Color3.fromRGB(100, 255, 100) or UI_COLORS.TextSecondary
    ownedLabel.TextScaled = true
    ownedLabel.Font = Enum.Font.Gotham
    ownedLabel.Parent = cardFrame

    -- 點擊事件
    local clickButton = Instance.new("TextButton")
    clickButton.Name = "ClickButton"
    clickButton.Size = UDim2.new(1, 0, 1, 0)
    clickButton.Position = UDim2.new(0, 0, 0, 0)
    clickButton.BackgroundTransparency = 1
    clickButton.Text = ""
    clickButton.Parent = cardFrame

    clickButton.MouseButton1Click:Connect(function()
        if petData.IsDiscovered then
            self:ShowPetDetails(petData)
        end
    end)

    -- 懸停效果
    clickButton.MouseEnter:Connect(function()
        TweenService:Create(cardFrame, TweenInfo.new(0.2), {BackgroundColor3 = UI_COLORS.ButtonHover}):Play()
    end)

    clickButton.MouseLeave:Connect(function()
        TweenService:Create(cardFrame, TweenInfo.new(0.2), {BackgroundColor3 = UI_COLORS.CardBackground}):Play()
    end)

    return cardFrame
end

-- 🎨 獲取寵物元素表情符號
function PetDexController:GetPetEmoji(element)
    local emojis = {
        Fire = "🔥",
        Water = "💧",
        Earth = "🌱",
        Air = "💨",
        Light = "✨",
        Dark = "🌙"
    }
    return emojis[element] or "❓"
end

-- 📊 創建詳細信息面板
function PetDexController:CreateDetailPanel(parentFrame)
    local detailFrame = Instance.new("Frame")
    detailFrame.Name = "DetailPanel"
    detailFrame.Size = UDim2.new(0.31, 0, 1, -70)
    detailFrame.Position = UDim2.new(0.67, 0, 0, 70)
    detailFrame.BackgroundColor3 = UI_COLORS.CardBackground
    detailFrame.BorderSizePixel = 0
    detailFrame.Parent = parentFrame

    local detailCorner = Instance.new("UICorner")
    detailCorner.CornerRadius = UDim.new(0, 8)
    detailCorner.Parent = detailFrame

    -- 滾動框
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Name = "ScrollFrame"
    scrollFrame.Size = UDim2.new(1, -10, 1, -10)
    scrollFrame.Position = UDim2.new(0, 5, 0, 5)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.BorderSizePixel = 0
    scrollFrame.ScrollBarThickness = 6
    scrollFrame.Parent = detailFrame

    -- 默認提示文字
    local hintLabel = Instance.new("TextLabel")
    hintLabel.Name = "HintLabel"
    hintLabel.Size = UDim2.new(1, 0, 0, 100)
    hintLabel.Position = UDim2.new(0, 0, 0.4, 0)
    hintLabel.BackgroundTransparency = 1
    hintLabel.Text = "點擊左側寵物卡片\n查看詳細信息"
    hintLabel.TextColor3 = UI_COLORS.TextSecondary
    hintLabel.TextScaled = true
    hintLabel.Font = Enum.Font.Gotham
    hintLabel.Parent = scrollFrame

    self.detailPanel = detailFrame
    self.detailScrollFrame = scrollFrame
    self.hintLabel = hintLabel

    return detailFrame
end

-- 🔍 顯示寵物詳細信息
function PetDexController:ShowPetDetails(petData)
    if not self.detailScrollFrame then return end

    -- 清除現有內容
    for _, child in pairs(self.detailScrollFrame:GetChildren()) do
        if child:IsA("GuiObject") then
            child:Destroy()
        end
    end

    local yOffset = 10

    -- 寵物大圖標
    local bigIcon = Instance.new("TextLabel")
    bigIcon.Name = "BigIcon"
    bigIcon.Size = UDim2.new(0.8, 0, 0, 120)
    bigIcon.Position = UDim2.new(0.1, 0, 0, yOffset)
    bigIcon.BackgroundColor3 = Color3.fromRGB(40, 40, 50)
    bigIcon.Text = self:GetPetEmoji(petData.Element)
    bigIcon.TextColor3 = UI_COLORS.TextPrimary
    bigIcon.TextScaled = true
    bigIcon.Font = Enum.Font.Gotham
    bigIcon.Parent = self.detailScrollFrame

    local bigIconCorner = Instance.new("UICorner")
    bigIconCorner.CornerRadius = UDim.new(0, 8)
    bigIconCorner.Parent = bigIcon

    yOffset = yOffset + 130

    -- 寵物名稱
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Name = "DetailName"
    nameLabel.Size = UDim2.new(1, 0, 0, 40)
    nameLabel.Position = UDim2.new(0, 0, 0, yOffset)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = petData.Name
    nameLabel.TextColor3 = UI_COLORS.TextPrimary
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.GothamBold
    nameLabel.Parent = self.detailScrollFrame

    yOffset = yOffset + 50

    -- 稀有度和元素
    local infoLabel = Instance.new("TextLabel")
    infoLabel.Name = "InfoLabel"
    infoLabel.Size = UDim2.new(1, 0, 0, 30)
    infoLabel.Position = UDim2.new(0, 0, 0, yOffset)
    infoLabel.BackgroundTransparency = 1
    infoLabel.Text = string.format("%s | %s系", petData.Rarity, petData.Element)
    infoLabel.TextColor3 = Utilities.GetRarityColor(petData.Rarity)
    infoLabel.TextScaled = true
    infoLabel.Font = Enum.Font.Gotham
    infoLabel.Parent = self.detailScrollFrame

    yOffset = yOffset + 40

    -- 描述
    local descLabel = Instance.new("TextLabel")
    descLabel.Name = "DescLabel"
    descLabel.Size = UDim2.new(1, 0, 0, 80)
    descLabel.Position = UDim2.new(0, 0, 0, yOffset)
    descLabel.BackgroundColor3 = Color3.fromRGB(35, 35, 45)
    descLabel.Text = petData.Description
    descLabel.TextColor3 = UI_COLORS.TextSecondary
    descLabel.TextScaled = true
    descLabel.TextWrapped = true
    descLabel.Font = Enum.Font.Gotham
    descLabel.Parent = self.detailScrollFrame

    local descCorner = Instance.new("UICorner")
    descCorner.CornerRadius = UDim.new(0, 6)
    descCorner.Parent = descLabel

    yOffset = yOffset + 90

    -- 屬性標題
    local statsTitle = Instance.new("TextLabel")
    statsTitle.Name = "StatsTitle"
    statsTitle.Size = UDim2.new(1, 0, 0, 30)
    statsTitle.Position = UDim2.new(0, 0, 0, yOffset)
    statsTitle.BackgroundTransparency = 1
    statsTitle.Text = "📊 基礎屬性"
    statsTitle.TextColor3 = UI_COLORS.TextPrimary
    statsTitle.TextScaled = true
    statsTitle.Font = Enum.Font.GothamBold
    statsTitle.Parent = self.detailScrollFrame

    yOffset = yOffset + 40

    -- 屬性列表
    local stats = {"HP", "ATK", "DEF", "SPD"}
    for i, statName in ipairs(stats) do
        local statFrame = Instance.new("Frame")
        statFrame.Name = "Stat_" .. statName
        statFrame.Size = UDim2.new(1, 0, 0, 25)
        statFrame.Position = UDim2.new(0, 0, 0, yOffset)
        statFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 55)
        statFrame.BorderSizePixel = 0
        statFrame.Parent = self.detailScrollFrame

        local statCorner = Instance.new("UICorner")
        statCorner.CornerRadius = UDim.new(0, 4)
        statCorner.Parent = statFrame

        local statLabel = Instance.new("TextLabel")
        statLabel.Name = "StatLabel"
        statLabel.Size = UDim2.new(0.3, 0, 1, 0)
        statLabel.Position = UDim2.new(0, 5, 0, 0)
        statLabel.BackgroundTransparency = 1
        statLabel.Text = statName .. ":"
        statLabel.TextColor3 = UI_COLORS.TextSecondary
        statLabel.TextScaled = true
        statLabel.Font = Enum.Font.Gotham
        statLabel.Parent = statFrame

        local statValue = Instance.new("TextLabel")
        statValue.Name = "StatValue"
        statValue.Size = UDim2.new(0.65, 0, 1, 0)
        statValue.Position = UDim2.new(0.3, 0, 0, 0)
        statValue.BackgroundTransparency = 1
        statValue.Text = tostring(petData.Stats[statName])
        statValue.TextColor3 = UI_COLORS.TextPrimary
        statValue.TextScaled = true
        statValue.Font = Enum.Font.GothamBold
        statValue.Parent = statFrame

        yOffset = yOffset + 30
    end

    yOffset = yOffset + 10

    -- 技能標題
    local skillsTitle = Instance.new("TextLabel")
    skillsTitle.Name = "SkillsTitle"
    skillsTitle.Size = UDim2.new(1, 0, 0, 30)
    skillsTitle.Position = UDim2.new(0, 0, 0, yOffset)
    skillsTitle.BackgroundTransparency = 1
    skillsTitle.Text = "⚔️ 技能列表"
    skillsTitle.TextColor3 = UI_COLORS.TextPrimary
    skillsTitle.TextScaled = true
    skillsTitle.Font = Enum.Font.GothamBold
    skillsTitle.Parent = self.detailScrollFrame

    yOffset = yOffset + 40

    -- 技能列表
    for i, skillName in ipairs(petData.Skills) do
        local skillFrame = Instance.new("Frame")
        skillFrame.Name = "Skill_" .. i
        skillFrame.Size = UDim2.new(1, 0, 0, 30)
        skillFrame.Position = UDim2.new(0, 0, 0, yOffset)
        skillFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 55)
        skillFrame.BorderSizePixel = 0
        skillFrame.Parent = self.detailScrollFrame

        local skillCorner = Instance.new("UICorner")
        skillCorner.CornerRadius = UDim.new(0, 4)
        skillCorner.Parent = skillFrame

        local skillLabel = Instance.new("TextLabel")
        skillLabel.Name = "SkillLabel"
        skillLabel.Size = UDim2.new(1, -10, 1, 0)
        skillLabel.Position = UDim2.new(0, 5, 0, 0)
        skillLabel.BackgroundTransparency = 1
        skillLabel.Text = "• " .. skillName
        skillLabel.TextColor3 = UI_COLORS.TextPrimary
        skillLabel.TextScaled = true
        skillLabel.Font = Enum.Font.Gotham
        skillLabel.Parent = skillFrame

        yOffset = yOffset + 35
    end

    -- 更新滾動框大小
    self.detailScrollFrame.CanvasSize = UDim2.new(0, 0, 0, yOffset + 20)
end

-- 🚀 打開寵物圖鑑UI
function PetDexController:OpenUI()
    if self.screenGui then
        self.screenGui:Destroy()
    end

    -- 創建主UI
    local screenGui, mainFrame = self:CreateMainUI()
    self.screenGui = screenGui
    self.mainFrame = mainFrame

    -- 創建寵物網格
    self:CreatePetGrid(mainFrame)

    -- 創建詳細面板
    self:CreateDetailPanel(mainFrame)

    -- 更新收集進度
    self:UpdateProgress()

    print("🐾 寵物圖鑑UI已打開")
end

-- ❌ 關閉UI
function PetDexController:CloseUI()
    if self.screenGui then
        -- 淡出動畫
        local tween = TweenService:Create(
            self.mainFrame,
            TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut),
            {Size = UDim2.new(0, 0, 0, 0), Position = UDim2.new(0.5, 0, 0.5, 0)}
        )

        tween:Play()
        tween.Completed:Connect(function()
            self.screenGui:Destroy()
            self.screenGui = nil
            self.mainFrame = nil
            self.detailPanel = nil
            self.detailScrollFrame = nil
            self.hintLabel = nil
        end)
    end

    print("🐾 寵物圖鑑UI已關閉")
end

-- 📊 更新收集進度
function PetDexController:UpdateProgress()
    if not self.screenGui then return end

    local discovered = 0
    local owned = 0
    local total = #TEST_PETS

    for _, petData in ipairs(TEST_PETS) do
        if petData.IsDiscovered then
            discovered = discovered + 1
        end
        if petData.IsOwned then
            owned = owned + 1
        end
    end

    local progressLabel = self.screenGui.MainFrame.HeaderFrame.ProgressLabel
    progressLabel.Text = string.format("收集進度: %d/%d (發現: %d)", owned, total, discovered)

    -- 更新進度條顏色
    local progressPercent = owned / total
    if progressPercent >= 0.8 then
        progressLabel.BackgroundColor3 = Color3.fromRGB(100, 255, 100)  -- 綠色
    elseif progressPercent >= 0.5 then
        progressLabel.BackgroundColor3 = Color3.fromRGB(255, 200, 100)  -- 橙色
    else
        progressLabel.BackgroundColor3 = Color3.fromRGB(255, 100, 100)  -- 紅色
    end
end

-- 🎯 初始化控制器
function PetDexController:Init()
    print("🐾 寵物圖鑑控制器已初始化")

    -- 綁定快捷鍵 (P鍵打開圖鑑)
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        if input.KeyCode == Enum.KeyCode.P then
            if self.screenGui then
                self:CloseUI()
            else
                self:OpenUI()
            end
        end
    end)

    -- 自動打開UI進行測試
    wait(2)  -- 等待2秒後自動打開
    self:OpenUI()
end

-- 🎮 啟動控制器
PetDexController:Init()

return PetDexController
