[package]
name = "mmorpg-pet-gacha/game"
version = "0.1.0"
registry = "https://github.com/UpliftGames/wally-index"
realm = "shared"

[dependencies]
# 核心框架
knit = "sleitnick/knit@^1.5"
roact = "roblox/roact@^1.4"
rodux = "roblox/rodux@^3.0"

# 數據管理
profileservice = "madstudioroblox/profileservice@^5.0"
datastore2 = "kampfkarren/datastore2@^2.0"

# 實用工具
janitor = "howmanysmall/janitor@^1.15"
signal = "sleitnick/signal@^1.5"
tableutil = "sleitnick/tableutil@^1.2"
promise = "evaera/promise@^4.0"

# 動畫和 UI
flipper = "reselim/flipper@^1.5"
spring = "quenty/spring@^1.0"

[server-dependencies]
# 服務端專用包

[dev-dependencies]
# 開發和測試工具
testez = "roblox/testez@^0.4"
