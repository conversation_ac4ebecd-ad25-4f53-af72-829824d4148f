[package]
name = "mmorpg-pet-gacha/game"
version = "0.1.0"
registry = "https://github.com/UpliftGames/wally-index"
realm = "shared"

[dependencies]
# 核心框架
knit = "sleitnick/knit@1.5.1"

# UI 框架
roact = "roblox/roact@1.4.4"

# 實用工具
janitor = "howmanysmall/janitor@1.15.4"
signal = "sleitnick/signal@1.5.0"
tableutil = "sleitnick/tableutil@1.2.4"
promise = "evaera/promise@4.0.0"

[server-dependencies]
# 服務端專用包

[dev-dependencies]
# 開發和測試工具
testez = "roblox/testez@^0.4"
