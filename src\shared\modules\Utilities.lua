-- 實用工具模塊
-- MMORPG Pet Gacha Game Utilities

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TableUtil = require(ReplicatedStorage.Packages.TableUtil)

local Utilities = {}

-- 🎲 隨機數工具
function Utilities.WeightedRandom(weights)
    local totalWeight = 0
    for _, weight in pairs(weights) do
        totalWeight = totalWeight + weight
    end
    
    local random = math.random() * totalWeight
    local currentWeight = 0
    
    for key, weight in pairs(weights) do
        currentWeight = currentWeight + weight
        if random <= currentWeight then
            return key
        end
    end
    
    -- 備用返回第一個鍵
    return next(weights)
end

-- 📊 經驗值計算
function Utilities.CalculateExpRequired(level)
    return math.floor(100 * (level ^ 1.5))
end

function Utilities.CalculateLevel(experience)
    local level = 1
    while Utilities.CalculateExpRequired(level + 1) <= experience do
        level = level + 1
    end
    return level
end

-- 🔢 數值格式化
function Utilities.FormatNumber(number)
    if number >= 1000000 then
        return string.format("%.1fM", number / 1000000)
    elseif number >= 1000 then
        return string.format("%.1fK", number / 1000)
    else
        return tostring(number)
    end
end

-- ⏰ 時間工具
function Utilities.FormatTime(seconds)
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    
    if hours > 0 then
        return string.format("%02d:%02d:%02d", hours, minutes, secs)
    else
        return string.format("%02d:%02d", minutes, secs)
    end
end

function Utilities.GetTimestamp()
    return os.time()
end

-- 🎨 顏色工具
function Utilities.GetRarityColor(rarity)
    local colors = {
        Common = Color3.fromRGB(255, 255, 255),    -- 白色
        Rare = Color3.fromRGB(0, 255, 0),          -- 綠色
        Epic = Color3.fromRGB(128, 0, 255),        -- 紫色
        Legendary = Color3.fromRGB(255, 165, 0)    -- 橙色
    }
    return colors[rarity] or colors.Common
end

-- 🔤 字串工具
function Utilities.GenerateId(length)
    length = length or 8
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    local result = ""
    
    for i = 1, length do
        local randomIndex = math.random(1, #chars)
        result = result .. string.sub(chars, randomIndex, randomIndex)
    end
    
    return result
end

-- 📋 深拷貝工具
function Utilities.DeepCopy(original)
    return TableUtil.Copy(original, true)
end

-- 🔍 搜索工具
function Utilities.FuzzySearch(query, items, keyFunc)
    query = string.lower(query)
    local results = {}
    
    for _, item in pairs(items) do
        local searchText = keyFunc and keyFunc(item) or tostring(item)
        searchText = string.lower(searchText)
        
        if string.find(searchText, query, 1, true) then
            table.insert(results, item)
        end
    end
    
    return results
end

-- 🎯 機率計算
function Utilities.CalculateDropChance(baseRate, level, bonusRate)
    bonusRate = bonusRate or 0
    local levelBonus = (level - 1) * 0.01  -- 每級增加1%
    return math.min(baseRate + levelBonus + bonusRate, 1.0)  -- 最大100%
end

-- 🔄 陣列工具
function Utilities.Shuffle(array)
    local shuffled = TableUtil.Copy(array)
    for i = #shuffled, 2, -1 do
        local j = math.random(i)
        shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
    end
    return shuffled
end

function Utilities.GetRandomElement(array)
    if #array == 0 then return nil end
    return array[math.random(#array)]
end

return Utilities
