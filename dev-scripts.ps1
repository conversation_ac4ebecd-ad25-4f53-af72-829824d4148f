# MMORPG Pet Gacha 開發腳本
# PowerShell 開發工具腳本

# 安裝依賴
function Install-Dependencies {
    Write-Host "📦 安裝 Wally 依賴..." -ForegroundColor Green
    wally install
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 依賴安裝完成!" -ForegroundColor Green
    } else {
        Write-Host "❌ 依賴安裝失敗!" -ForegroundColor Red
    }
}

# 啟動 Rojo 服務
function Start-Rojo {
    Write-Host "🚀 啟動 Rojo 服務..." -ForegroundColor Green
    rojo serve
}

# 構建項目
function Build-Project {
    Write-Host "🔨 構建項目..." -ForegroundColor Green
    rojo build -o "MMORPG-Pet-Gacha.rbxl"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 項目構建完成!" -ForegroundColor Green
    } else {
        Write-Host "❌ 項目構建失敗!" -ForegroundColor Red
    }
}

# 代碼檢查
function Check-Code {
    Write-Host "🔍 執行代碼檢查..." -ForegroundColor Green
    selene src/
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 代碼檢查通過!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 發現代碼問題!" -ForegroundColor Yellow
    }
}

# 格式化代碼
function Format-Code {
    Write-Host "🎨 格式化代碼..." -ForegroundColor Green
    stylua src/
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 代碼格式化完成!" -ForegroundColor Green
    } else {
        Write-Host "❌ 代碼格式化失敗!" -ForegroundColor Red
    }
}

# 完整開發流程
function Start-Development {
    Write-Host "🎮 開始 MMORPG Pet Gacha 開發流程..." -ForegroundColor Cyan
    
    Install-Dependencies
    Check-Code
    Format-Code
    Start-Rojo
}

# 顯示幫助
function Show-Help {
    Write-Host "MMORPG Pet Gacha 開發工具" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    Write-Host "Install-Dependencies  - 安裝 Wally 依賴"
    Write-Host "Start-Rojo           - 啟動 Rojo 服務"
    Write-Host "Build-Project        - 構建項目"
    Write-Host "Check-Code           - 代碼檢查"
    Write-Host "Format-Code          - 格式化代碼"
    Write-Host "Start-Development    - 完整開發流程"
    Write-Host "Show-Help            - 顯示此幫助"
}

# 默認顯示幫助
Show-Help
