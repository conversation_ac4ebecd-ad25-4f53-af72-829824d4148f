# Selene 代碼檢查配置
# MMORPG Pet Gacha Game

std = "roblox"

[rules]
# 啟用的規則
almost_swapped = "warn"
empty_if = "warn"
if_same_then_else = "warn"
mismatched_arg_count = "warn"
mixed_table = "warn"
multiple_statements = "warn"
parenthese_conditions = "warn"
suspicious_reverse_loop = "warn"
unbalanced_assignments = "warn"
undefined_variable = "warn"
unused_variable = "warn"

# 禁用的規則
incorrect_standard_library_use = "allow"
