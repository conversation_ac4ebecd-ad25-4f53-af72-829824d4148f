-- 簡化的 React 風格組件系統
-- MMORPG Pet Gacha Game - Simple React-like Component System

local SimpleReact = {}

-- 🎯 組件基類
local Component = {}
Component.__index = Component

function Component.new(props)
    local self = setmetatable({}, Component)
    self.props = props or {}
    self.state = {}
    self.children = {}
    self.instance = nil
    return self
end

function Component:setState(newState)
    for key, value in pairs(newState) do
        self.state[key] = value
    end
    self:forceUpdate()
end

function Component:forceUpdate()
    if self.instance then
        self:updateInstance()
    end
end

function Component:render()
    -- 子類需要重寫此方法
    error("Component:render() must be implemented by subclass")
end

function Component:mount(parent)
    local element = self:render()
    self.instance = self:createElement(element, parent)
    return self.instance
end

function Component:unmount()
    if self.instance then
        self.instance:Destroy()
        self.instance = nil
    end
end

function Component:updateInstance()
    if self.instance then
        local parent = self.instance.Parent
        self:unmount()
        self:mount(parent)
    end
end

-- 🏗️ 創建元素
function Component:createElement(element, parent)
    if type(element) == "table" then
        local instance = Instance.new(element.type)
        
        -- 設置屬性
        if element.props then
            for prop, value in pairs(element.props) do
                if prop ~= "children" then
                    instance[prop] = value
                end
            end
        end
        
        -- 設置父級
        if parent then
            instance.Parent = parent
        end
        
        -- 創建子元素
        if element.props and element.props.children then
            for _, child in ipairs(element.props.children) do
                if type(child) == "table" then
                    self:createElement(child, instance)
                elseif type(child) == "string" then
                    -- 文本節點
                    if instance:IsA("TextLabel") or instance:IsA("TextButton") then
                        instance.Text = child
                    end
                end
            end
        end
        
        return instance
    end
    
    return nil
end

-- 🎨 創建元素的便捷函數
function SimpleReact.createElement(elementType, props, ...)
    local children = {...}
    if props then
        props.children = children
    else
        props = {children = children}
    end
    
    return {
        type = elementType,
        props = props
    }
end

-- 📱 常用組件創建函數
function SimpleReact.Frame(props, ...)
    return SimpleReact.createElement("Frame", props, ...)
end

function SimpleReact.TextLabel(props, ...)
    return SimpleReact.createElement("TextLabel", props, ...)
end

function SimpleReact.TextButton(props, ...)
    return SimpleReact.createElement("TextButton", props, ...)
end

function SimpleReact.ScrollingFrame(props, ...)
    return SimpleReact.createElement("ScrollingFrame", props, ...)
end

function SimpleReact.ScreenGui(props, ...)
    return SimpleReact.createElement("ScreenGui", props, ...)
end

-- 🎯 創建組件類
function SimpleReact.createComponent(renderFunction)
    local ComponentClass = setmetatable({}, {__index = Component})
    ComponentClass.__index = ComponentClass
    
    function ComponentClass.new(props)
        local self = Component.new(props)
        setmetatable(self, ComponentClass)
        return self
    end
    
    ComponentClass.render = renderFunction
    
    return ComponentClass
end

-- 🚀 掛載組件到父級
function SimpleReact.mount(component, parent)
    if type(component) == "table" and component.mount then
        return component:mount(parent)
    elseif type(component) == "function" then
        -- 函數組件
        local element = component()
        local tempComponent = Component.new()
        return tempComponent:createElement(element, parent)
    end
end

-- 📋 導出組件基類
SimpleReact.Component = Component

return SimpleReact
