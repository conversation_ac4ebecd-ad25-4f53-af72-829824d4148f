{"name": "MMORPG-Pet-Gacha", "tree": {"$className": "DataModel", "ReplicatedStorage": {"$className": "ReplicatedStorage", "Shared": {"$path": "src/shared"}, "Packages": {"$path": "Packages"}}, "ServerScriptService": {"$className": "ServerScriptService", "Server": {"$path": "src/server"}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "Client": {"$path": "src/client"}}}, "Workspace": {"$className": "Workspace", "$properties": {}, "$ignoreUnknownInstances": true}, "Lighting": {"$className": "Lighting", "$properties": {}, "$ignoreUnknownInstances": true}, "SoundService": {"$className": "SoundService", "$properties": {}, "$ignoreUnknownInstances": true}}}