-- 遊戲常數配置
-- MMORPG Pet Gacha Game Constants

local GameConstants = {}

-- 🎮 遊戲基本設定
GameConstants.GAME_NAME = "MMORPG Pet Gacha"
GameConstants.VERSION = "0.1.0"
GameConstants.MAX_PLAYERS = 50

-- 👤 角色系統
GameConstants.CHARACTER = {
    MAX_LEVEL = 100,
    BASE_HP = 100,
    BASE_MP = 50,
    BASE_ATK = 10,
    BASE_DEF = 5,
    BASE_SPD = 10,
    EXP_MULTIPLIER = 1.2
}

-- 🐾 寵物系統
GameConstants.PET = {
    MAX_OWNED = 200,
    MAX_TEAM = 6,
    MAX_LEVEL = 50,
    BASE_STATS = {
        HP = 80,
        ATK = 15,
        DEF = 8,
        SPD = 12
    }
}

-- 🎲 扭蛋系統
GameConstants.GACHA = {
    NORMAL_COST = 100,      -- 普通扭蛋費用
    PREMIUM_COST = 300,     -- 高級扭蛋費用
    PITY_THRESHOLD = 90,    -- 保底次數
    
    -- 稀有度機率 (%)
    RARITY_RATES = {
        Common = 60,        -- 普通 60%
        Rare = 25,          -- 稀有 25%
        Epic = 12,          -- 史詩 12%
        Legendary = 3       -- 傳說 3%
    }
}

-- 💰 經濟系統
GameConstants.ECONOMY = {
    STARTING_GOLD = 1000,
    STARTING_GEMS = 500,
    DAILY_LOGIN_GOLD = 100,
    DAILY_LOGIN_GEMS = 50
}

-- ⚔️ 戰鬥系統
GameConstants.COMBAT = {
    TURN_TIME_LIMIT = 30,   -- 回合時間限制(秒)
    MAX_BATTLE_TIME = 300,  -- 最大戰鬥時間(秒)
    CRITICAL_RATE = 0.05,   -- 基礎暴擊率
    CRITICAL_DAMAGE = 1.5   -- 暴擊傷害倍數
}

-- 🏆 成就系統
GameConstants.ACHIEVEMENTS = {
    FIRST_PET = "獲得第一隻寵物",
    PET_COLLECTOR_10 = "收集10隻寵物",
    PET_COLLECTOR_50 = "收集50隻寵物",
    GACHA_MASTER = "進行100次扭蛋",
    LEVEL_10 = "角色達到10級"
}

return GameConstants
